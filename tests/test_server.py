"""
服务器模块测试
"""

import pytest
import threading
import time
import socket
from src.server.canvas_state import CanvasState, CanvasElement
from src.server.server import UserManager
from src.protocol.constants import ElementType


class TestCanvasElement:
    """画板元素测试"""
    
    def test_element_creation(self):
        """测试元素创建"""
        attributes = {"startX": 10, "startY": 20, "endX": 100, "endY": 200, "color": "#FF0000"}
        element = CanvasElement("elem1", "user1", ElementType.LINE, attributes)
        
        assert element.element_id == "elem1"
        assert element.user_id == "user1"
        assert element.type == ElementType.LINE
        assert element.attributes == attributes
    
    def test_to_dict(self):
        """测试转换为字典"""
        attributes = {"radius": 50, "centerX": 100, "centerY": 100}
        element = CanvasElement("elem2", "user2", ElementType.CIRCLE, attributes)
        
        data = element.to_dict()
        expected = {
            "element_id": "elem2",
            "user_id": "user2",
            "type": ElementType.CIRCLE,
            "attributes": attributes
        }
        
        assert data == expected
    
    def test_from_dict(self):
        """测试从字典创建"""
        data = {
            "element_id": "elem3",
            "user_id": "user3",
            "type": ElementType.RECTANGLE,
            "attributes": {"x": 10, "y": 20, "width": 100, "height": 50}
        }
        
        element = CanvasElement.from_dict(data)
        assert element.element_id == "elem3"
        assert element.user_id == "user3"
        assert element.type == ElementType.RECTANGLE
        assert element.attributes == data["attributes"]


class TestCanvasState:
    """画板状态测试"""
    
    def test_add_element(self):
        """测试添加元素"""
        canvas = CanvasState()
        attributes = {"startX": 0, "startY": 0, "endX": 100, "endY": 100}
        
        element_id = canvas.add_element("user1", ElementType.LINE, attributes)
        
        assert element_id is not None
        assert len(element_id) > 0
        assert canvas.get_element_count() == 1
        
        element = canvas.get_element(element_id)
        assert element is not None
        assert element.user_id == "user1"
        assert element.type == ElementType.LINE
    
    def test_remove_element(self):
        """测试删除元素"""
        canvas = CanvasState()
        attributes = {"radius": 50}
        
        element_id = canvas.add_element("user1", ElementType.CIRCLE, attributes)
        assert canvas.get_element_count() == 1
        
        # 删除存在的元素
        result = canvas.remove_element(element_id)
        assert result == True
        assert canvas.get_element_count() == 0
        
        # 删除不存在的元素
        result = canvas.remove_element("nonexistent")
        assert result == False
    
    def test_clear_canvas(self):
        """测试清空画板"""
        canvas = CanvasState()
        
        # 添加多个元素
        for i in range(5):
            canvas.add_element(f"user{i}", ElementType.LINE, {"x": i})
        
        assert canvas.get_element_count() == 5
        
        canvas.clear_canvas()
        assert canvas.get_element_count() == 0
    
    def test_get_all_elements(self):
        """测试获取所有元素"""
        canvas = CanvasState()
        
        # 添加元素
        attrs1 = {"type": "line"}
        attrs2 = {"type": "circle"}
        
        id1 = canvas.add_element("user1", ElementType.LINE, attrs1)
        id2 = canvas.add_element("user2", ElementType.CIRCLE, attrs2)
        
        all_elements = canvas.get_all_elements()
        assert len(all_elements) == 2
        
        # 检查元素内容
        element_ids = [elem["element_id"] for elem in all_elements]
        assert id1 in element_ids
        assert id2 in element_ids
    
    def test_get_elements_by_user(self):
        """测试按用户获取元素"""
        canvas = CanvasState()
        
        # 用户1添加2个元素
        canvas.add_element("user1", ElementType.LINE, {"a": 1})
        canvas.add_element("user1", ElementType.CIRCLE, {"b": 2})
        
        # 用户2添加1个元素
        canvas.add_element("user2", ElementType.RECTANGLE, {"c": 3})
        
        user1_elements = canvas.get_elements_by_user("user1")
        user2_elements = canvas.get_elements_by_user("user2")
        
        assert len(user1_elements) == 2
        assert len(user2_elements) == 1
        
        # 检查用户ID
        for element in user1_elements:
            assert element.user_id == "user1"
        
        for element in user2_elements:
            assert element.user_id == "user2"
    
    def test_thread_safety(self):
        """测试线程安全"""
        canvas = CanvasState()
        results = []
        
        def add_elements():
            for i in range(10):
                element_id = canvas.add_element(f"user{threading.current_thread().ident}", 
                                              ElementType.LINE, {"index": i})
                results.append(element_id)
        
        # 创建多个线程同时添加元素
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=add_elements)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 检查结果
        assert len(results) == 50  # 5个线程 × 10个元素
        assert canvas.get_element_count() == 50
        assert len(set(results)) == 50  # 所有ID都是唯一的


class TestUserManager:
    """用户管理器测试"""
    
    def test_add_user(self):
        """测试添加用户"""
        manager = UserManager()
        
        # 模拟客户端处理器
        class MockClientHandler:
            def __init__(self, name):
                self.name = name
        
        handler1 = MockClientHandler("client1")
        handler2 = MockClientHandler("client2")
        
        user_id1 = manager.add_user("user1", handler1)
        user_id2 = manager.add_user("user2", handler2)
        
        assert user_id1 != user_id2
        assert manager.get_user_count() == 2
        
        # 检查用户获取
        assert manager.get_user(user_id1) == handler1
        assert manager.get_user(user_id2) == handler2
    
    def test_remove_user(self):
        """测试删除用户"""
        manager = UserManager()
        
        class MockClientHandler:
            pass
        
        handler = MockClientHandler()
        user_id = manager.add_user("user1", handler)
        
        assert manager.get_user_count() == 1
        
        manager.remove_user(user_id)
        assert manager.get_user_count() == 0
        assert manager.get_user(user_id) is None
    
    def test_get_all_users(self):
        """测试获取所有用户"""
        manager = UserManager()
        
        class MockClientHandler:
            def __init__(self, name):
                self.name = name
        
        handlers = [MockClientHandler(f"client{i}") for i in range(3)]
        user_ids = []
        
        for i, handler in enumerate(handlers):
            user_id = manager.add_user(f"user{i}", handler)
            user_ids.append(user_id)
        
        all_users = manager.get_all_users()
        assert len(all_users) == 3
        
        for handler in handlers:
            assert handler in all_users


if __name__ == '__main__':
    pytest.main([__file__])
