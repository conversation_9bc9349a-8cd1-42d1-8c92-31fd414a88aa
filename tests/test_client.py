"""
客户端模块测试
"""

import pytest
import threading
import time
from unittest.mock import Mock, patch
from src.client.client import NetworkClient
from src.client.canvas import DrawingElement, CanvasWidget
from src.protocol.message import Message, MessageBuilder
from src.protocol.constants import MessageType, ElementType


class TestDrawingElement:
    """绘图元素测试"""
    
    def test_line_element(self):
        """测试直线元素"""
        attributes = {
            "startX": 10, "startY": 20,
            "endX": 100, "endY": 200,
            "color": "#FF0000", "thickness": 2
        }
        element = DrawingElement("elem1", "user1", ElementType.LINE, attributes)
        
        assert element.element_id == "elem1"
        assert element.user_id == "user1"
        assert element.type == ElementType.LINE
        assert element.attributes == attributes
    
    def test_rectangle_element(self):
        """测试矩形元素"""
        attributes = {
            "x": 10, "y": 20, "width": 100, "height": 50,
            "strokeColor": "#00FF00", "thickness": 1
        }
        element = DrawingElement("elem2", "user2", ElementType.RECTANGLE, attributes)
        
        assert element.type == ElementType.RECTANGLE
        assert element.attributes["width"] == 100
        assert element.attributes["height"] == 50
    
    def test_circle_element(self):
        """测试圆形元素"""
        attributes = {
            "centerX": 100, "centerY": 100, "radius": 50,
            "strokeColor": "#0000FF", "thickness": 3
        }
        element = DrawingElement("elem3", "user3", ElementType.CIRCLE, attributes)
        
        assert element.type == ElementType.CIRCLE
        assert element.attributes["radius"] == 50
    
    def test_freehand_element(self):
        """测试自由画笔元素"""
        points = [
            {"x": 10, "y": 10},
            {"x": 20, "y": 15},
            {"x": 30, "y": 25}
        ]
        attributes = {
            "points": points,
            "color": "#FFFF00", "thickness": 2
        }
        element = DrawingElement("elem4", "user4", ElementType.FREEHAND, attributes)
        
        assert element.type == ElementType.FREEHAND
        assert len(element.attributes["points"]) == 3
    
    def test_text_element(self):
        """测试文本元素"""
        attributes = {
            "x": 50, "y": 60,
            "content": "Hello World",
            "font_size": 14, "color": "#000000"
        }
        element = DrawingElement("elem5", "user5", ElementType.TEXT, attributes)
        
        assert element.type == ElementType.TEXT
        assert element.attributes["content"] == "Hello World"


class MockCanvas:
    """模拟Canvas对象"""
    
    def __init__(self):
        self.items = {}
        self.next_id = 1
    
    def create_line(self, *args, **kwargs):
        item_id = self.next_id
        self.next_id += 1
        self.items[item_id] = ("line", args, kwargs)
        return item_id
    
    def create_rectangle(self, *args, **kwargs):
        item_id = self.next_id
        self.next_id += 1
        self.items[item_id] = ("rectangle", args, kwargs)
        return item_id
    
    def create_oval(self, *args, **kwargs):
        item_id = self.next_id
        self.next_id += 1
        self.items[item_id] = ("oval", args, kwargs)
        return item_id
    
    def create_text(self, *args, **kwargs):
        item_id = self.next_id
        self.next_id += 1
        self.items[item_id] = ("text", args, kwargs)
        return item_id
    
    def delete(self, item_id):
        if item_id in self.items:
            del self.items[item_id]


class TestDrawingElementCanvas:
    """绘图元素画布测试"""
    
    def test_draw_line_on_canvas(self):
        """测试在画布上绘制直线"""
        canvas = MockCanvas()
        attributes = {
            "startX": 10, "startY": 20,
            "endX": 100, "endY": 200,
            "color": "#FF0000", "thickness": 2
        }
        element = DrawingElement("elem1", "user1", ElementType.LINE, attributes)
        
        canvas_id = element.draw_on_canvas(canvas)
        
        assert canvas_id is not None
        assert canvas_id in canvas.items
        
        item_type, args, kwargs = canvas.items[canvas_id]
        assert item_type == "line"
        assert kwargs["fill"] == "#FF0000"
        assert kwargs["width"] == 2
    
    def test_draw_rectangle_on_canvas(self):
        """测试在画布上绘制矩形"""
        canvas = MockCanvas()
        attributes = {
            "x": 10, "y": 20, "width": 100, "height": 50,
            "strokeColor": "#00FF00", "thickness": 1
        }
        element = DrawingElement("elem2", "user2", ElementType.RECTANGLE, attributes)
        
        canvas_id = element.draw_on_canvas(canvas)
        
        assert canvas_id is not None
        item_type, args, kwargs = canvas.items[canvas_id]
        assert item_type == "rectangle"
        assert kwargs["outline"] == "#00FF00"
        assert kwargs["width"] == 1
    
    def test_draw_circle_on_canvas(self):
        """测试在画布上绘制圆形"""
        canvas = MockCanvas()
        attributes = {
            "centerX": 100, "centerY": 100, "radius": 50,
            "strokeColor": "#0000FF", "thickness": 3
        }
        element = DrawingElement("elem3", "user3", ElementType.CIRCLE, attributes)
        
        canvas_id = element.draw_on_canvas(canvas)
        
        assert canvas_id is not None
        item_type, args, kwargs = canvas.items[canvas_id]
        assert item_type == "oval"
        assert kwargs["outline"] == "#0000FF"
        assert kwargs["width"] == 3


class TestNetworkClient:
    """网络客户端测试"""
    
    def test_client_creation(self):
        """测试客户端创建"""
        callback = Mock()
        client = NetworkClient(callback)
        
        assert client.message_callback == callback
        assert client.connected == False
        assert client.authenticated == False
        assert client.user_id is None
    
    def test_message_handling(self):
        """测试消息处理"""
        received_messages = []
        
        def message_callback(message):
            received_messages.append(message)
        
        client = NetworkClient(message_callback)
        
        # 模拟认证响应
        auth_message = MessageBuilder.auth_response("success", "user123", "OK", [])
        client._handle_message(auth_message)
        
        assert client.authenticated == True
        assert client.user_id == "user123"
        assert len(received_messages) == 1
    
    def test_element_creation(self):
        """测试元素创建"""
        client = NetworkClient()
        client.authenticated = True
        
        # 模拟socket
        with patch.object(client, 'send_message') as mock_send:
            mock_send.return_value = True
            
            attributes = {"startX": 10, "startY": 20, "endX": 100, "endY": 200}
            result = client.add_element(ElementType.LINE, attributes)
            
            assert result == True
            assert mock_send.called
            
            # 检查发送的消息
            call_args = mock_send.call_args[0][0]
            assert call_args.message_type == MessageType.ADD_ELEMENT_REQUEST
            assert call_args.payload["type"] == ElementType.LINE
            assert call_args.payload["attributes"] == attributes
    
    def test_canvas_clear(self):
        """测试清空画板"""
        client = NetworkClient()
        client.authenticated = True
        
        with patch.object(client, 'send_message') as mock_send:
            mock_send.return_value = True
            
            result = client.clear_canvas()
            
            assert result == True
            assert mock_send.called
            
            call_args = mock_send.call_args[0][0]
            assert call_args.message_type == MessageType.CLEAR_CANVAS_REQUEST
    
    def test_unauthenticated_operations(self):
        """测试未认证状态下的操作"""
        client = NetworkClient()
        client.authenticated = False
        
        # 未认证时不能添加元素
        result = client.add_element(ElementType.LINE, {})
        assert result == False
        
        # 未认证时不能清空画板
        result = client.clear_canvas()
        assert result == False


if __name__ == '__main__':
    pytest.main([__file__])
