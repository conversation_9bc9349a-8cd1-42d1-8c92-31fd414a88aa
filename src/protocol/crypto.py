"""
简单加密功能
实现密码哈希和基础加密功能
"""

import hashlib
import secrets
from typing import <PERSON>ple


def hash_password(password: str, salt: str = None) -> Tuple[str, str]:
    """
    对密码进行哈希处理

    Args:
        password: 原始密码
        salt: 盐值，如果为None则自动生成

    Returns:
        (hashed_password, salt): 哈希后的密码和盐值
    """
    if salt is None:
        salt = secrets.token_hex(16)

    # 使用SHA256进行哈希
    password_salt = (password + salt).encode("utf-8")
    hashed = hashlib.sha256(password_salt).hexdigest()

    return hashed, salt


def verify_password(password: str, hashed_password: str, salt: str) -> bool:
    """
    验证密码

    Args:
        password: 原始密码
        hashed_password: 存储的哈希密码
        salt: 盐值

    Returns:
        bool: 密码是否正确
    """
    computed_hash, _ = hash_password(password, salt)
    return computed_hash == hashed_password


def simple_hash(text: str) -> str:
    """
    简单哈希函数，用于客户端密码传输

    Args:
        text: 要哈希的文本

    Returns:
        str: 哈希值
    """
    return hashlib.sha256(text.encode("utf-8")).hexdigest()


# 简单的XOR加密（仅用于演示，实际应用中应使用更强的加密）
def xor_encrypt(data: bytes, key: bytes) -> bytes:
    """
    XOR加密/解密

    Args:
        data: 要加密的数据
        key: 密钥

    Returns:
        bytes: 加密后的数据
    """
    if not key:
        return data

    result = bytearray()
    key_len = len(key)

    for i, byte in enumerate(data):
        result.append(byte ^ key[i % key_len])

    return bytes(result)


def xor_decrypt(data: bytes, key: bytes) -> bytes:
    """
    XOR解密（与加密相同）

    Args:
        data: 要解密的数据
        key: 密钥

    Returns:
        bytes: 解密后的数据
    """
    return xor_encrypt(data, key)


def generate_session_key() -> bytes:
    """
    生成会话密钥

    Returns:
        bytes: 32字节的随机密钥
    """
    return secrets.token_bytes(32)
