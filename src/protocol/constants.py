"""
网络协议常量定义
定义消息类型、协议版本等常量
"""

# 协议版本
PROTOCOL_VERSION = 0x01

# 消息头长度（固定8字节）
HEADER_LENGTH = 8

# 消息类型定义
class MessageType:
    # 认证相关
    AUTH_REQUEST = 0x0001
    AUTH_RESPONSE = 0x0002
    
    # 元素操作
    ADD_ELEMENT_REQUEST = 0x0003
    ELEMENT_ADDED_NOTIFY = 0x0004
    DELETE_ELEMENT_REQUEST = 0x0005
    ELEMENT_DELETED_NOTIFY = 0x0006
    
    # 画板操作
    CLEAR_CANVAS_REQUEST = 0x0007
    CANVAS_CLEARED_NOTIFY = 0x0008
    
    # 用户管理
    USER_JOINED_NOTIFY = 0x0009
    USER_LEFT_NOTIFY = 0x000A
    
    # 心跳
    HEARTBEAT_REQUEST = 0x000B
    HEARTBEAT_RESPONSE = 0x000C
    
    # 错误处理
    ERROR_NOTIFY = 0xFFFF

# 元素类型定义
class ElementType:
    LINE = "line"
    RECTANGLE = "rectangle"
    CIRCLE = "circle"
    FREEHAND = "freehand"
    TEXT = "text"

# 认证状态
class AuthStatus:
    SUCCESS = "success"
    FAILURE = "failure"

# 错误代码
class ErrorCode:
    INVALID_REQUEST = 400
    UNAUTHORIZED = 401
    ELEMENT_NOT_FOUND = 404
    INTERNAL_ERROR = 500

# 默认配置
DEFAULT_SERVER_HOST = "localhost"
DEFAULT_SERVER_PORT = 8888
HEARTBEAT_INTERVAL = 30  # 秒
CONNECTION_TIMEOUT = 60  # 秒

# 颜色预设
DEFAULT_COLORS = [
    "#000000",  # 黑色
    "#FF0000",  # 红色
    "#00FF00",  # 绿色
    "#0000FF",  # 蓝色
    "#FFFF00",  # 黄色
    "#FF00FF",  # 紫色
    "#00FFFF",  # 青色
    "#FFFFFF",  # 白色
]

# 画笔粗细预设
DEFAULT_THICKNESS = [1, 3, 5, 8]
