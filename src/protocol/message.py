"""
消息格式和编解码功能
实现协议消息的序列化和反序列化
"""

import json
import struct
from typing import Dict, Any, Tuple, Optional
from .constants import PROTOCOL_VERSION, HEADER_LENGTH


class Message:
    """协议消息类"""
    
    def __init__(self, message_type: int, payload: Dict[str, Any] = None):
        self.version = PROTOCOL_VERSION
        self.message_type = message_type
        self.payload = payload or {}
        self.reserved = 0x00
    
    def encode(self) -> bytes:
        """将消息编码为字节流"""
        # 序列化payload为JSON
        payload_json = json.dumps(self.payload, ensure_ascii=False)
        payload_bytes = payload_json.encode('utf-8')
        payload_length = len(payload_bytes)
        
        # 构造消息头（8字节）
        # 格式: Version(1) + MessageType(2) + PayloadLength(4) + Reserved(1)
        header = struct.pack('!BHI B', 
                           self.version,
                           self.message_type, 
                           payload_length,
                           self.reserved)
        
        return header + payload_bytes
    
    @classmethod
    def decode_header(cls, header_bytes: bytes) -> Tuple[int, int, int, int]:
        """解码消息头"""
        if len(header_bytes) != HEADER_LENGTH:
            raise ValueError(f"Invalid header length: {len(header_bytes)}")
        
        version, message_type, payload_length, reserved = struct.unpack('!BHI B', header_bytes)
        return version, message_type, payload_length, reserved
    
    @classmethod
    def decode(cls, data: bytes) -> 'Message':
        """从字节流解码消息"""
        if len(data) < HEADER_LENGTH:
            raise ValueError("Data too short for header")
        
        # 解码消息头
        header_bytes = data[:HEADER_LENGTH]
        version, message_type, payload_length, reserved = cls.decode_header(header_bytes)
        
        # 验证协议版本
        if version != PROTOCOL_VERSION:
            raise ValueError(f"Unsupported protocol version: {version}")
        
        # 验证数据长度
        if len(data) < HEADER_LENGTH + payload_length:
            raise ValueError("Incomplete message data")
        
        # 解码payload
        payload_bytes = data[HEADER_LENGTH:HEADER_LENGTH + payload_length]
        payload = {}
        if payload_length > 0:
            try:
                payload_json = payload_bytes.decode('utf-8')
                payload = json.loads(payload_json)
            except (UnicodeDecodeError, json.JSONDecodeError) as e:
                raise ValueError(f"Invalid payload format: {e}")
        
        # 创建消息对象
        message = cls(message_type, payload)
        message.version = version
        message.reserved = reserved
        
        return message
    
    def __str__(self) -> str:
        return f"Message(type=0x{self.message_type:04X}, payload={self.payload})"
    
    def __repr__(self) -> str:
        return self.__str__()


class MessageBuilder:
    """消息构建器，提供便捷的消息创建方法"""
    
    @staticmethod
    def auth_request(username: str, password_hash: str) -> Message:
        """创建认证请求消息"""
        from .constants import MessageType
        payload = {
            "username": username,
            "password_hash": password_hash
        }
        return Message(MessageType.AUTH_REQUEST, payload)
    
    @staticmethod
    def auth_response(status: str, user_id: str = None, message: str = "", 
                     canvas_state: list = None) -> Message:
        """创建认证响应消息"""
        from .constants import MessageType
        payload = {
            "status": status,
            "message": message
        }
        if user_id:
            payload["user_id"] = user_id
        if canvas_state:
            payload["canvas_state"] = canvas_state
        return Message(MessageType.AUTH_RESPONSE, payload)
    
    @staticmethod
    def add_element_request(element_type: str, attributes: Dict[str, Any]) -> Message:
        """创建添加元素请求消息"""
        from .constants import MessageType
        payload = {
            "type": element_type,
            "attributes": attributes
        }
        return Message(MessageType.ADD_ELEMENT_REQUEST, payload)
    
    @staticmethod
    def element_added_notify(element_id: str, user_id: str, element_type: str, 
                           attributes: Dict[str, Any]) -> Message:
        """创建元素已添加通知消息"""
        from .constants import MessageType
        payload = {
            "element_id": element_id,
            "user_id": user_id,
            "type": element_type,
            "attributes": attributes
        }
        return Message(MessageType.ELEMENT_ADDED_NOTIFY, payload)
    
    @staticmethod
    def clear_canvas_request() -> Message:
        """创建清空画板请求消息"""
        from .constants import MessageType
        return Message(MessageType.CLEAR_CANVAS_REQUEST, {})
    
    @staticmethod
    def canvas_cleared_notify() -> Message:
        """创建画板已清空通知消息"""
        from .constants import MessageType
        return Message(MessageType.CANVAS_CLEARED_NOTIFY, {})
    
    @staticmethod
    def user_joined_notify(user_id: str, username: str) -> Message:
        """创建用户加入通知消息"""
        from .constants import MessageType
        payload = {
            "user_id": user_id,
            "username": username
        }
        return Message(MessageType.USER_JOINED_NOTIFY, payload)
    
    @staticmethod
    def user_left_notify(user_id: str) -> Message:
        """创建用户离开通知消息"""
        from .constants import MessageType
        payload = {
            "user_id": user_id
        }
        return Message(MessageType.USER_LEFT_NOTIFY, payload)
    
    @staticmethod
    def heartbeat_request() -> Message:
        """创建心跳请求消息"""
        from .constants import MessageType
        return Message(MessageType.HEARTBEAT_REQUEST, {})
    
    @staticmethod
    def heartbeat_response() -> Message:
        """创建心跳响应消息"""
        from .constants import MessageType
        return Message(MessageType.HEARTBEAT_RESPONSE, {})
    
    @staticmethod
    def error_notify(code: int, message: str) -> Message:
        """创建错误通知消息"""
        from .constants import MessageType
        payload = {
            "code": code,
            "message": message
        }
        return Message(MessageType.ERROR_NOTIFY, payload)
