"""
GUI界面模块
实现轻量级的用户界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import threading
from typing import Optional
from .client import NetworkClient
from .canvas import CanvasWidget
from ..protocol.message import Message
from ..protocol.constants import (
    MessageType, ElementType, DEFAULT_COLORS, DEFAULT_THICKNESS,
    DEFAULT_SERVER_HOST, DEFAULT_SERVER_PORT
)
from ..utils.logger import get_logger


class LoginDialog:
    """登录对话框"""
    
    def __init__(self, parent):
        self.result = None
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("连接到服务器")
        self.dialog.geometry("300x200")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        self._create_widgets()
    
    def _create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 服务器地址
        ttk.Label(main_frame, text="服务器地址:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.host_var = tk.StringVar(value=DEFAULT_SERVER_HOST)
        ttk.Entry(main_frame, textvariable=self.host_var, width=20).grid(row=0, column=1, pady=2)
        
        # 端口
        ttk.Label(main_frame, text="端口:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.port_var = tk.StringVar(value=str(DEFAULT_SERVER_PORT))
        ttk.Entry(main_frame, textvariable=self.port_var, width=20).grid(row=1, column=1, pady=2)
        
        # 用户名
        ttk.Label(main_frame, text="用户名:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.username_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.username_var, width=20).grid(row=2, column=1, pady=2)
        
        # 密码
        ttk.Label(main_frame, text="密码:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.password_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.password_var, width=20, show="*").grid(row=3, column=1, pady=2)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="连接", command=self._on_connect).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=self._on_cancel).pack(side=tk.LEFT, padx=5)
        
        # 绑定回车键
        self.dialog.bind('<Return>', lambda e: self._on_connect())
        
        # 设置焦点
        self.username_var.trace('w', self._update_password_hint)
        
    def _update_password_hint(self, *args):
        """更新密码提示"""
        # 简单提示：密码就是用户名
        pass
    
    def _on_connect(self):
        """连接按钮点击"""
        host = self.host_var.get().strip()
        port_str = self.port_var.get().strip()
        username = self.username_var.get().strip()
        password = self.password_var.get()
        
        if not all([host, port_str, username]):
            messagebox.showerror("错误", "请填写所有必填字段")
            return
        
        try:
            port = int(port_str)
        except ValueError:
            messagebox.showerror("错误", "端口必须是数字")
            return
        
        # 简单验证：密码为空时使用用户名作为密码
        if not password:
            password = username
        
        self.result = {
            'host': host,
            'port': port,
            'username': username,
            'password': password
        }
        self.dialog.destroy()
    
    def _on_cancel(self):
        """取消按钮点击"""
        self.dialog.destroy()
    
    def show(self):
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result


class SharedCanvasGUI:
    """共享画板GUI主界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("多人共享画板")
        self.root.geometry("1000x700")
        
        # 网络客户端
        self.client: Optional[NetworkClient] = None
        
        # GUI组件
        self.canvas_widget: Optional[CanvasWidget] = None
        self.status_var = tk.StringVar(value="未连接")
        self.users_var = tk.StringVar(value="用户: 0")
        
        self.logger = get_logger('GUI')
        
        self._create_widgets()
        self._setup_menu()
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 工具栏
        self._create_toolbar(main_frame)
        
        # 画板区域
        canvas_frame = ttk.Frame(main_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.canvas_widget = CanvasWidget(canvas_frame)
        self.canvas_widget.set_element_callback(self._on_element_created)
        
        # 状态栏
        self._create_statusbar(main_frame)
    
    def _create_toolbar(self, parent):
        """创建工具栏"""
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, pady=(0, 5))
        
        # 连接按钮
        ttk.Button(toolbar, text="连接", command=self._connect_to_server).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="断开", command=self._disconnect_from_server).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 绘图工具
        ttk.Label(toolbar, text="工具:").pack(side=tk.LEFT, padx=2)
        
        self.tool_var = tk.StringVar(value=ElementType.LINE)
        tools = [
            (ElementType.LINE, "直线"),
            (ElementType.RECTANGLE, "矩形"),
            (ElementType.CIRCLE, "圆形"),
            (ElementType.FREEHAND, "画笔")
        ]
        
        for tool_type, tool_name in tools:
            ttk.Radiobutton(toolbar, text=tool_name, variable=self.tool_var, 
                          value=tool_type, command=self._on_tool_changed).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 颜色选择
        ttk.Label(toolbar, text="颜色:").pack(side=tk.LEFT, padx=2)
        
        self.color_var = tk.StringVar(value=DEFAULT_COLORS[0])
        color_frame = ttk.Frame(toolbar)
        color_frame.pack(side=tk.LEFT, padx=2)
        
        for i, color in enumerate(DEFAULT_COLORS[:6]):  # 只显示前6种颜色
            btn = tk.Button(color_frame, bg=color, width=2, height=1,
                          command=lambda c=color: self._set_color(c))
            btn.grid(row=0, column=i, padx=1)
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 画笔粗细
        ttk.Label(toolbar, text="粗细:").pack(side=tk.LEFT, padx=2)
        
        self.thickness_var = tk.StringVar(value=str(DEFAULT_THICKNESS[0]))
        thickness_combo = ttk.Combobox(toolbar, textvariable=self.thickness_var, 
                                     values=[str(t) for t in DEFAULT_THICKNESS],
                                     width=5, state="readonly")
        thickness_combo.pack(side=tk.LEFT, padx=2)
        thickness_combo.bind('<<ComboboxSelected>>', self._on_thickness_changed)
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 清空按钮
        ttk.Button(toolbar, text="清空画板", command=self._clear_canvas).pack(side=tk.LEFT, padx=2)
    
    def _create_statusbar(self, parent):
        """创建状态栏"""
        statusbar = ttk.Frame(parent)
        statusbar.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(statusbar, textvariable=self.status_var).pack(side=tk.LEFT)
        ttk.Label(statusbar, textvariable=self.users_var).pack(side=tk.RIGHT)
    
    def _setup_menu(self):
        """设置菜单"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="连接服务器", command=self._connect_to_server)
        file_menu.add_command(label="断开连接", command=self._disconnect_from_server)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self._on_closing)
        
        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="清空画板", command=self._clear_canvas)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="关于", command=self._show_about)
    
    def _connect_to_server(self):
        """连接到服务器"""
        if self.client and self.client.is_connected():
            messagebox.showinfo("提示", "已经连接到服务器")
            return
        
        # 显示登录对话框
        login_dialog = LoginDialog(self.root)
        result = login_dialog.show()
        
        if not result:
            return
        
        # 在后台线程中连接
        def connect_thread():
            try:
                self.client = NetworkClient(self._on_message_received)
                
                self.status_var.set("正在连接...")
                
                if self.client.connect(result['host'], result['port']):
                    if self.client.authenticate(result['username'], result['password']):
                        self.status_var.set(f"已连接到 {result['host']}:{result['port']}")
                    else:
                        self.status_var.set("认证失败")
                        self.client.disconnect()
                        self.client = None
                else:
                    self.status_var.set("连接失败")
                    self.client = None
                    
            except Exception as e:
                self.logger.error(f"Connection error: {e}")
                self.status_var.set("连接错误")
                if self.client:
                    self.client.disconnect()
                    self.client = None
        
        threading.Thread(target=connect_thread, daemon=True).start()
    
    def _disconnect_from_server(self):
        """断开服务器连接"""
        if self.client:
            self.client.disconnect()
            self.client = None
        
        self.status_var.set("未连接")
        self.users_var.set("用户: 0")
    
    def _on_element_created(self, element_type: str, attributes: dict):
        """元素创建回调"""
        if self.client and self.client.is_authenticated():
            self.client.add_element(element_type, attributes)
    
    def _on_tool_changed(self):
        """工具改变回调"""
        if self.canvas_widget:
            self.canvas_widget.set_tool(self.tool_var.get())
    
    def _set_color(self, color: str):
        """设置颜色"""
        self.color_var.set(color)
        if self.canvas_widget:
            self.canvas_widget.set_color(color)
    
    def _on_thickness_changed(self, event=None):
        """画笔粗细改变回调"""
        if self.canvas_widget:
            thickness = int(self.thickness_var.get())
            self.canvas_widget.set_thickness(thickness)
    
    def _clear_canvas(self):
        """清空画板"""
        if self.client and self.client.is_authenticated():
            result = messagebox.askyesno("确认", "确定要清空画板吗？这将删除所有绘图内容。")
            if result:
                self.client.clear_canvas()
    
    def _on_message_received(self, message: Message):
        """消息接收回调"""
        # 在主线程中处理GUI更新
        self.root.after(0, lambda: self._handle_message(message))
    
    def _handle_message(self, message: Message):
        """处理接收到的消息"""
        if message.message_type == MessageType.AUTH_RESPONSE:
            self._handle_auth_response(message)
        elif message.message_type == MessageType.ELEMENT_ADDED_NOTIFY:
            self._handle_element_added(message)
        elif message.message_type == MessageType.CANVAS_CLEARED_NOTIFY:
            self._handle_canvas_cleared(message)
        elif message.message_type == MessageType.USER_JOINED_NOTIFY:
            self._handle_user_joined(message)
        elif message.message_type == MessageType.USER_LEFT_NOTIFY:
            self._handle_user_left(message)
        elif message.message_type == MessageType.ERROR_NOTIFY:
            self._handle_error(message)
    
    def _handle_auth_response(self, message: Message):
        """处理认证响应"""
        status = message.payload.get("status")
        if status == "success":
            # 加载初始画板状态
            canvas_state = message.payload.get("canvas_state", [])
            for element_data in canvas_state:
                self.canvas_widget.add_element(
                    element_data["element_id"],
                    element_data["user_id"],
                    element_data["type"],
                    element_data["attributes"]
                )
            
            self.logger.info(f"Loaded {len(canvas_state)} elements from server")
        else:
            error_msg = message.payload.get("message", "认证失败")
            messagebox.showerror("认证失败", error_msg)
    
    def _handle_element_added(self, message: Message):
        """处理元素添加通知"""
        payload = message.payload
        self.canvas_widget.add_element(
            payload["element_id"],
            payload["user_id"],
            payload["type"],
            payload["attributes"]
        )
    
    def _handle_canvas_cleared(self, message: Message):
        """处理画板清空通知"""
        self.canvas_widget.clear_canvas()
    
    def _handle_user_joined(self, message: Message):
        """处理用户加入通知"""
        username = message.payload.get("username", "Unknown")
        self.logger.info(f"User joined: {username}")
    
    def _handle_user_left(self, message: Message):
        """处理用户离开通知"""
        user_id = message.payload.get("user_id", "Unknown")
        self.logger.info(f"User left: {user_id}")
    
    def _handle_error(self, message: Message):
        """处理错误通知"""
        error_msg = message.payload.get("message", "未知错误")
        messagebox.showerror("服务器错误", error_msg)
    
    def _show_about(self):
        """显示关于对话框"""
        messagebox.showinfo("关于", 
                          "多人共享画板 v1.0\n\n"
                          "基于Python实现的实时协作绘图工具\n"
                          "支持多用户同时绘图和实时同步")
    
    def _on_closing(self):
        """窗口关闭事件"""
        if self.client:
            self.client.disconnect()
        self.root.destroy()
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()


def main():
    """主函数"""
    app = SharedCanvasGUI()
    app.run()


if __name__ == '__main__':
    main()
