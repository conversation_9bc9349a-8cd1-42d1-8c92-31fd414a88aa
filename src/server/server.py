"""
多人共享画板服务器
主服务器实现，处理客户端连接和消息广播
"""

import socket
import threading
import signal
import sys
from typing import Dict, List, Optional
from .client_handler import ClientHandler
from ..canvas.state import CanvasState
from ..protocol.constants import DEFAULT_SERVER_HOST, DEFAULT_SERVER_PORT
from ..protocol.message import Message
from ..utils.logger import get_logger


class UserManager:
    """用户管理器"""

    def __init__(self):
        self.users: Dict[str, ClientHandler] = {}
        self.lock = threading.RLock()
        self.user_counter = 0

    def add_user(self, username: str, client_handler: ClientHandler) -> str:
        """添加用户"""
        with self.lock:
            self.user_counter += 1
            user_id = f"user_{self.user_counter}"
            self.users[user_id] = client_handler
            return user_id

    def remove_user(self, user_id: str):
        """移除用户"""
        with self.lock:
            if user_id in self.users:
                del self.users[user_id]

    def get_user(self, user_id: str) -> Optional[ClientHandler]:
        """获取用户"""
        with self.lock:
            return self.users.get(user_id)

    def get_all_users(self) -> List[ClientHandler]:
        """获取所有用户"""
        with self.lock:
            return list(self.users.values())

    def get_user_count(self) -> int:
        """获取用户数量"""
        with self.lock:
            return len(self.users)


class SharedCanvasServer:
    """共享画板服务器"""

    def __init__(
        self, host: str = DEFAULT_SERVER_HOST, port: int = DEFAULT_SERVER_PORT
    ):
        self.host = host
        self.port = port
        self.socket: Optional[socket.socket] = None
        self.running = False

        # 核心组件
        self.canvas_state = CanvasState()
        self.user_manager = UserManager()

        # 客户端处理线程列表
        self.client_threads: List[threading.Thread] = []

        self.logger = get_logger("Server")

        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def start(self):
        """启动服务器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.bind((self.host, self.port))
            self.socket.listen(10)

            self.running = True
            self.logger.info(f"Server started on {self.host}:{self.port}")

            self._accept_connections()

        except Exception as e:
            self.logger.error(f"Failed to start server: {e}")
            self.stop()

    def _accept_connections(self):
        """接受客户端连接"""
        while self.running:
            try:
                client_socket, client_address = self.socket.accept()

                # 创建客户端处理器
                client_handler = ClientHandler(
                    client_socket,
                    client_address,
                    self.canvas_state,
                    self.user_manager,
                    self.broadcast_message,
                )

                # 启动客户端处理线程
                client_thread = threading.Thread(
                    target=client_handler.start,
                    name=f"Client-{client_address[0]}:{client_address[1]}",
                )
                client_thread.daemon = True
                client_thread.start()

                self.client_threads.append(client_thread)

                self.logger.info(f"New client connected: {client_address}")

            except OSError:
                # Socket已关闭
                break
            except Exception as e:
                if self.running:
                    self.logger.error(f"Error accepting connection: {e}")

    def broadcast_message(self, message: Message, exclude: ClientHandler = None):
        """广播消息给所有已认证的客户端"""
        users = self.user_manager.get_all_users()
        failed_users = []

        for user in users:
            if user == exclude:
                continue

            if not user.send_message(message):
                failed_users.append(user)

        # 清理发送失败的用户
        for user in failed_users:
            if user.user_id:
                self.user_manager.remove_user(user.user_id)

        self.logger.debug(
            f"Broadcasted message to {len(users) - len(failed_users)} users"
        )

    def get_server_stats(self) -> Dict:
        """获取服务器统计信息"""
        return {
            "connected_users": self.user_manager.get_user_count(),
            "canvas_elements": self.canvas_state.get_element_count(),
            "running": self.running,
        }

    def stop(self):
        """停止服务器"""
        self.logger.info("Stopping server...")
        self.running = False

        if self.socket:
            try:
                self.socket.close()
            except:
                pass

        # 等待客户端线程结束
        for thread in self.client_threads:
            if thread.is_alive():
                thread.join(timeout=1.0)

        self.logger.info("Server stopped")

    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"Received signal {signum}, shutting down...")
        self.stop()
        sys.exit(0)


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="Shared Canvas Server")
    parser.add_argument(
        "--host",
        default=DEFAULT_SERVER_HOST,
        help=f"Server host (default: {DEFAULT_SERVER_HOST})",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=DEFAULT_SERVER_PORT,
        help=f"Server port (default: {DEFAULT_SERVER_PORT})",
    )
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    args = parser.parse_args()

    # 设置日志级别
    if args.debug:
        import logging

        logging.getLogger().setLevel(logging.DEBUG)

    # 创建并启动服务器
    server = SharedCanvasServer(args.host, args.port)

    try:
        server.start()
    except KeyboardInterrupt:
        server.stop()


if __name__ == "__main__":
    main()
